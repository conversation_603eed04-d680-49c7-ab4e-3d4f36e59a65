# إضافة حقل الوصف إلى جدول المواد الدراسية

## نظرة عامة
تم إضافة حقل `description` إلى جدول `subjects` في Supabase لتمكين إضافة وصف لكل مادة دراسية يمكن تعديله من قاعدة البيانات.

## التغييرات المطلوبة

### 1. تحديث قاعدة البيانات في Supabase
قم بتشغيل الاستعلام التالي في Supabase SQL Editor:

```sql
-- إضافة حقل description إلى جدول subjects
ALTER TABLE subjects 
ADD COLUMN description TEXT;

-- إضافة تعليق على العمود
COMMENT ON COLUMN subjects.description IS 'وصف المادة الدراسية';
```

### 2. الملفات التي تم تحديثها

#### أ. أنواع Supabase (`integrations/supabase/types.ts`)
- تم إضافة `description: string | null` إلى Row, Insert, و Update interfaces

#### ب. أنواع TypeScript (`data/types.ts`)
- تم إضافة `description?: string` إلى interface Subject

#### ج. دوال جلب البيانات (`backend/utils/supabaseLoader.ts`)
- تم تحديث `fetchSubjectsFromSupabase()` و `fetchSubjectsForYearFromSupabase()`
- إضافة mapping للحقل الجديد: `description: subject.description || undefined`

#### د. صفحة المادة (`app/subject/[subjectId]/subject-client.tsx`)
- تم إضافة عرض الوصف تحت عنوان المادة
- الوصف يظهر فقط إذا كان موجوداً في قاعدة البيانات

## كيفية إضافة الأوصاف

### من Supabase Dashboard:
1. اذهب إلى Table Editor
2. اختر جدول `subjects`
3. قم بتحرير أي مادة وأضف الوصف في حقل `description`

### من SQL Editor:
```sql
UPDATE subjects 
SET description = 'وصف المادة هنا'
WHERE id = 'معرف_المادة';
```

## مثال على الأوصاف

```sql
UPDATE subjects 
SET description = 'تعلم أساسيات الرياضيات والعمليات الحسابية المختلفة مع تمارين تطبيقية شاملة'
WHERE name = 'الرياضيات';

UPDATE subjects 
SET description = 'استكشف قواعد اللغة العربية والأدب والتعبير مع نصوص متنوعة وتمارين تفاعلية'
WHERE name = 'اللغة العربية';
```

## التصميم
- الوصف يظهر تحت عنوان المادة مباشرة
- يستخدم الكلاس `arabic-text` للنصوص العربية
- محدود بعرض أقصى `max-w-3xl` ومتوسط في الصفحة
- لون النص `text-foreground` وحجم `text-lg`

## ملاحظات
- الحقل اختياري (nullable) لذا لن يؤثر على المواد الموجودة
- إذا لم يكن هناك وصف، لن يظهر أي شيء في الصفحة
- يمكن تعديل الوصف في أي وقت من Supabase Dashboard
