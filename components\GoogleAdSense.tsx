'use client'

import { useEffect } from 'react'
import Script from 'next/script'

interface GoogleAdSenseProps {
  publisherId?: string
}

export default function GoogleAdSense({ publisherId }: GoogleAdSenseProps) {
  const adSenseId = publisherId || process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_ID

  useEffect(() => {
    // Initialize AdSense consent mode
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'default', {
        ad_storage: 'denied',
        ad_user_data: 'denied',
        ad_personalization: 'denied',
        analytics_storage: 'denied'
      })
    }
  }, [])

  if (!adSenseId) {
    return null
  }

  return (
    <Script
      id="google-adsense"
      src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adSenseId}`}
      crossOrigin="anonymous"
      strategy="afterInteractive"
      onLoad={() => {
        console.log('Google AdSense loaded successfully')
      }}
      onError={(e) => {
        console.error('Failed to load Google AdSense:', e)
      }}
    />
  )
}

// Ad Unit Component for displaying ads
interface AdUnitProps {
  slot: string
  style?: React.CSSProperties
  className?: string
  format?: 'auto' | 'rectangle' | 'vertical' | 'horizontal'
  responsive?: boolean
}

export function AdUnit({ 
  slot, 
  style = { display: 'block' }, 
  className = '',
  format = 'auto',
  responsive = true 
}: AdUnitProps) {
  const adSenseId = process.env.NEXT_PUBLIC_GOOGLE_ADSENSE_ID

  useEffect(() => {
    try {
      if (typeof window !== 'undefined' && window.adsbygoogle) {
        (window.adsbygoogle = window.adsbygoogle || []).push({})
      }
    } catch (error) {
      console.error('AdSense error:', error)
    }
  }, [])

  if (!adSenseId || !slot) {
    return null
  }

  return (
    <div className={`ad-container ${className}`}>
      <ins
        className="adsbygoogle"
        style={style}
        data-ad-client={adSenseId}
        data-ad-slot={slot}
        data-ad-format={format}
        data-full-width-responsive={responsive.toString()}
      />
    </div>
  )
}

// Predefined Ad Components for common placements
export function HeaderAd() {
  return (
    <div className="w-full max-w-4xl mx-auto my-4">
      <AdUnit
        slot={process.env.NEXT_PUBLIC_ADSENSE_HEADER_SLOT || ''}
        style={{ display: 'block', width: '100%', height: '90px' }}
        format="horizontal"
        className="header-ad"
      />
    </div>
  )
}

export function SidebarAd() {
  return (
    <div className="w-full max-w-xs">
      <AdUnit
        slot={process.env.NEXT_PUBLIC_ADSENSE_SIDEBAR_SLOT || ''}
        style={{ display: 'block', width: '300px', height: '250px' }}
        format="rectangle"
        className="sidebar-ad"
      />
    </div>
  )
}

export function ContentAd() {
  return (
    <div className="w-full max-w-2xl mx-auto my-6">
      <AdUnit
        slot={process.env.NEXT_PUBLIC_ADSENSE_CONTENT_SLOT || ''}
        style={{ display: 'block', width: '100%', height: '280px' }}
        format="auto"
        className="content-ad"
      />
    </div>
  )
}

export function FooterAd() {
  return (
    <div className="w-full max-w-4xl mx-auto my-4">
      <AdUnit
        slot={process.env.NEXT_PUBLIC_ADSENSE_FOOTER_SLOT || ''}
        style={{ display: 'block', width: '100%', height: '90px' }}
        format="horizontal"
        className="footer-ad"
      />
    </div>
  )
}

// Mobile-specific ad component
export function MobileAd() {
  return (
    <div className="w-full block md:hidden">
      <AdUnit
        slot={process.env.NEXT_PUBLIC_ADSENSE_MOBILE_SLOT || ''}
        style={{ display: 'block', width: '100%', height: '50px' }}
        format="auto"
        className="mobile-ad"
      />
    </div>
  )
}

// In-article ad component
export function InArticleAd() {
  return (
    <div className="w-full max-w-2xl mx-auto my-8">
      <div className="text-center text-xs text-muted-foreground mb-2">إعلان</div>
      <AdUnit
        slot={process.env.NEXT_PUBLIC_ADSENSE_ARTICLE_SLOT || ''}
        style={{ display: 'block', width: '100%', minHeight: '200px' }}
        format="auto"
        className="in-article-ad"
      />
    </div>
  )
}

// Declare global types for TypeScript
declare global {
  interface Window {
    adsbygoogle: Record<string, unknown>[]
    gtag: (...args: unknown[]) => void
  }
}
