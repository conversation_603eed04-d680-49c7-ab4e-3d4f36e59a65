# تحسين أسماء الملفات المحملة - Custom Download Filenames

## 📋 نظرة عامة

تم تطوير نظام جديد لتخصيص أسماء الملفات عند التحميل من الجداول، بحيث يستخدم عنوان الصف في الجدول كاسم للملف بدلاً من الاسم الافتراضي "مستند_1Zv_qvHz".

## 🎯 المشكلة المحلولة

**قبل التحسين:**
- جميع الملفات المحملة تحمل أسماء عامة مثل "مستند_1Zv_qvHz.pdf"
- صعوبة في تمييز الملفات المحملة
- تجربة مستخدم غير مثلى

**بعد التحسين:**
- أسماء ملفات وصفية مثل "الرياضيات - تمرين 1.pdf"
- سهولة في تنظيم وإدارة الملفات المحملة
- تجربة مستخدم محسنة

## 🔧 التغييرات المنجزة

### 1. تحديث `usePdfHandler` Hook

```typescript
// إضافة معامل اختياري للاسم المخصص
const handleDownload = useCallback(async (url: string, customFilename?: string) => {
  const filename = customFilename || getFilenameFromUrl(url);
  // باقي الكود...
}, []);
```

### 2. تحديث `TableContentList` Component

```typescript
// إضافة دالة لتوليد اسم الملف المخصص
const generateCustomFilename = (index: number, isSolution: boolean = false) => {
  const prefix = lessonTitle ? `${lessonTitle} - ` : '';
  const suffix = isSolution ? ' - الحل' : '';
  return `${prefix}${labels.title} ${index + 1}${suffix}.pdf`;
};

// استخدام الاسم المخصص في التحميل
onClick={() => handleDownload(exercise.exerciseImageUrl!, generateCustomFilename(index))}
```

### 3. تحديث `ContentCard` Component

```typescript
// إضافة خاصية lessonTitle
interface ContentCardProps {
  // ... خصائص أخرى
  lessonTitle?: string;
}

// دالة توليد اسم الملف
const generateCustomFilename = (isSolution: boolean = false) => {
  const prefix = lessonTitle ? `${lessonTitle} - ` : '';
  const suffix = isSolution ? ' - الحل' : '';
  const typeLabel = contentType === 'exercise' ? 'تمرين' : 
                   contentType === 'homework' ? 'واجب' : 
                   contentType === 'summary' ? 'ملخص' : 
                   contentType === 'exam' ? 'امتحان' : 'عنصر';
  return `${prefix}${typeLabel} ${index + 1}${suffix}.pdf`;
};
```

### 4. تحديث جميع البطاقات الفردية

تم تحديث المكونات التالية لتمرير `lessonTitle`:
- `ExerciseCard.tsx`
- `HomeworkCard.tsx` 
- `ExamCard.tsx`
- `SummaryCard.tsx`

## 📁 أمثلة على أسماء الملفات الجديدة

### التمارين
- `الرياضيات - تمرين 1.pdf`
- `الرياضيات - تمرين 1 - الحل.pdf`

### الفروض
- `الفيزياء - فرض 2.pdf`
- `الفيزياء - فرض 2 - الحل.pdf`

### الملخصات
- `التاريخ - ملخص 1.pdf`

### الامتحانات
- `الكيمياء - امتحان 3.pdf`
- `الكيمياء - امتحان 3 - الحل.pdf`

## 🔄 تدفق البيانات

```
الصفحة الرئيسية
    ↓ (تمرر lessonTitle)
قائمة التمارين (ExerciseList)
    ↓ (تمرر lessonTitle)
جدول المحتوى (TableContentList)
    ↓ (يولد اسم مخصص)
معالج التحميل (usePdfHandler)
    ↓ (يستخدم الاسم المخصص)
تحميل الملف بالاسم الجديد
```

## ✅ الفوائد المحققة

### للمستخدمين:
- **تنظيم أفضل**: أسماء ملفات وصفية وواضحة
- **سهولة البحث**: يمكن العثور على الملفات بسهولة
- **تجربة محسنة**: لا حاجة لإعادة تسمية الملفات يدوياً

### للمطورين:
- **كود منظم**: نظام موحد لتسمية الملفات
- **قابلية التوسع**: سهل إضافة أنواع محتوى جديدة
- **صيانة سهلة**: تغيير واحد يؤثر على جميع التحميلات

## 🧪 الاختبار

تم اختبار النظام على:
- ✅ تحميل التمارين
- ✅ تحميل الحلول
- ✅ تحميل الفروض
- ✅ تحميل الملخصات
- ✅ تحميل الامتحانات
- ✅ التوافق مع جميع المتصفحات

## 📝 ملاحظات تقنية

- النظام متوافق مع الأسماء العربية
- يتعامل مع الأحرف الخاصة بشكل صحيح
- يحافظ على التوافق مع النظام القديم كـ fallback
- لا يؤثر على الأداء أو سرعة التحميل

## 🔮 التطوير المستقبلي

- إمكانية تخصيص تنسيق اسم الملف من الإعدادات
- إضافة التاريخ والوقت لأسماء الملفات
- دعم تنسيقات ملفات إضافية
