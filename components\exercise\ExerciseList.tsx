'use client';

import TableContentList from '@/components/ui/table-content-list';
import { Exercise } from '@/data/types';

interface ExerciseListProps {
  exercises: Exercise[];
  lessonTitle?: string;
  subjectName?: string;
  yearName?: string;
  levelName?: string;
}

const ExerciseList = ({
  exercises,
  lessonTitle,
  subjectName,
  yearName,
  levelName
}: ExerciseListProps) => {
  return (
    <TableContentList
      exercises={exercises}
      contentType="exercise"
      lessonTitle={lessonTitle}
      subjectName={subjectName}
      yearName={yearName}
      levelName={levelName}
    />
  );
};

export default ExerciseList;
