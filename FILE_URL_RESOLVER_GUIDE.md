# 🔗 دليل نظام تحويل روابط الملفات

## 📋 نظرة عامة

تم إنشاء نظام ذكي لتحويل الروابط النسبية إلى روابط كاملة تلقائياً، مما يسمح بتغيير خادم الملفات دون الحاجة لتعديل قاعدة البيانات.

## 🎯 المشكلة التي يحلها

### قبل النظام الجديد:
```sql
-- في قاعدة البيانات
exercise_image_url: 'https://d.talamid.ma/files/math/exercise1.pdf'
solution_image_url: 'https://d.talamid.ma/files/math/solution1.pdf'
```

**المشاكل:**
- ❌ صعوبة تغيير الخادم (يتطلب تحديث آلاف الروابط)
- ❌ مشاكل CORS مع النطاقات الفرعية
- ❌ صعوبة النقل بين البيئات

### بعد النظام الجديد:
```sql
-- في قاعدة البيانات (روابط نسبية)
exercise_image_url: 'files/math/exercise1.pdf'
solution_image_url: 'files/math/solution1.pdf'
```

**المميزات:**
- ✅ تحويل تلقائي إلى روابط كاملة
- ✅ سهولة تغيير الخادم من متغيرات البيئة
- ✅ دعم خوادم احتياطية متعددة
- ✅ حل مشاكل CORS

## 🔧 كيفية العمل

### 1. تخزين الروابط النسبية في قاعدة البيانات:
```sql
INSERT INTO exercises (id, exercise_image_url) 
VALUES ('ex1', 'files/math/grade7/exercise1.pdf');
```

### 2. التحويل التلقائي في الكود:
```typescript
// الرابط من قاعدة البيانات: 'files/math/grade7/exercise1.pdf'
// يتم تحويله تلقائياً إلى: 'https://d.talamid.ma/files/math/grade7/exercise1.pdf'

import { resolveFileUrl } from '@/utils/file-url-resolver';

const fullUrl = resolveFileUrl('files/math/grade7/exercise1.pdf');
// النتيجة: 'https://d.talamid.ma/files/math/grade7/exercise1.pdf'
```

## ⚙️ إعدادات متغيرات البيئة

### في `.env.local`:
```bash
# خادم الملفات الأساسي
NEXT_PUBLIC_FILE_SERVER_URL=https://d.talamid.ma

# خوادم احتياطية (مفصولة بفواصل)
NEXT_PUBLIC_FILE_FALLBACK_DOMAINS=https://d.talamid.ma,https://files.talamid.ma,https://cdn.talamid.ma

# بادئة المسار (اختيارية)
NEXT_PUBLIC_FILE_PATH_PREFIX=
```

## 🚀 الاستخدام

### 1. تحويل رابط واحد:
```typescript
import { resolveFileUrl } from '@/utils/file-url-resolver';

const relativeUrl = 'files/math/exercise1.pdf';
const absoluteUrl = resolveFileUrl(relativeUrl);
// النتيجة: 'https://d.talamid.ma/files/math/exercise1.pdf'
```

### 2. تحويل عدة روابط:
```typescript
import { resolveMultipleFileUrls } from '@/utils/file-url-resolver';

const relativeUrls = [
  'files/math/exercise1.pdf',
  'files/science/lab1.pdf',
  null, // سيتم تجاهله
  'files/arabic/text1.pdf'
];

const absoluteUrls = resolveMultipleFileUrls(relativeUrls);
// النتيجة: ['https://d.tolabi.net/files/math/exercise1.pdf', ...]
```

### 3. تحويل روابط التمارين:
```typescript
import { resolveExerciseUrls } from '@/utils/file-url-resolver';

const exercise = {
  id: 'ex1',
  exerciseImageUrl: 'files/math/exercise1.pdf',
  solutionImageUrl: 'files/math/solution1.pdf'
};

const resolvedExercise = resolveExerciseUrls(exercise);
// النتيجة: روابط كاملة لجميع الملفات
```

## 🔄 تحديث قاعدة البيانات

### تحويل الروابط الموجودة من كاملة إلى نسبية:
```sql
-- تحديث جدول التمارين
UPDATE exercises
SET exercise_image_url = REPLACE(exercise_image_url, 'https://d.talamid.ma/', '')
WHERE exercise_image_url LIKE 'https://d.talamid.ma/%';

-- تحديث جدول الفروض
UPDATE homeworks
SET exercise_image_url = REPLACE(exercise_image_url, 'https://d.talamid.ma/', '')
WHERE exercise_image_url LIKE 'https://d.talamid.ma/%';
```

### إضافة بيانات جديدة بروابط نسبية:
```sql
INSERT INTO exercises (id, exercise_image_url, solution_image_url) 
VALUES (
  'new_ex1', 
  'files/math/new_exercise.pdf',  -- رابط نسبي
  'files/math/new_solution.pdf'   -- رابط نسبي
);
```

## 🌐 تغيير الخادم

### لتغيير خادم الملفات:
1. **حدث متغير البيئة فقط:**
```bash
# من
NEXT_PUBLIC_FILE_SERVER_URL=https://d.talamid.ma

# إلى
NEXT_PUBLIC_FILE_SERVER_URL=https://cdn.talamid.ma
```

2. **أعد تشغيل التطبيق**
3. **جميع الروابط ستتحول تلقائياً!**

## 🛡️ الخوادم الاحتياطية

إذا فشل الخادم الأساسي، يمكن استخدام:
```typescript
import { getFallbackUrl } from '@/utils/file-url-resolver';

// الحصول على رابط احتياطي
const fallbackUrl = getFallbackUrl('files/math/exercise1.pdf', 0);
// النتيجة: 'https://files.talamid.ma/files/math/exercise1.pdf'
```

## 🔍 التحقق والاختبار

### التحقق من صحة الرابط:
```typescript
import { validateFileUrl } from '@/utils/file-url-resolver';

const isValid = validateFileUrl('files/math/exercise1.pdf');
// النتيجة: true أو false
```

### عرض إعدادات الخادم:
```typescript
import { getFileServerConfig, logFileServerConfig } from '@/utils/file-url-resolver';

// عرض الإعدادات الحالية
const config = getFileServerConfig();
console.log(config);

// تسجيل الإعدادات (في بيئة التطوير فقط)
logFileServerConfig();
```

## 📊 مثال كامل

```typescript
// في component
import { resolveFileUrl } from '@/utils/file-url-resolver';

function ExerciseCard({ exercise }) {
  // تحويل الروابط تلقائياً
  const exerciseUrl = resolveFileUrl(exercise.exerciseImageUrl);
  const solutionUrl = resolveFileUrl(exercise.solutionImageUrl);

  return (
    <div>
      <button onClick={() => downloadFile(exerciseUrl)}>
        تحميل التمرين
      </button>
      {solutionUrl && (
        <button onClick={() => downloadFile(solutionUrl)}>
          تحميل الحل
        </button>
      )}
    </div>
  );
}
```

## 🎉 النتيجة النهائية

- ✅ **مرونة كاملة**: تغيير الخادم دون تعديل قاعدة البيانات
- ✅ **حل مشاكل CORS**: لا مزيد من مشاكل النطاقات الفرعية
- ✅ **سهولة الصيانة**: إعداد واحد يؤثر على جميع الملفات
- ✅ **دعم خوادم متعددة**: خوادم احتياطية تلقائية
- ✅ **تحويل تلقائي**: لا حاجة لتذكر تحويل الروابط يدوياً
