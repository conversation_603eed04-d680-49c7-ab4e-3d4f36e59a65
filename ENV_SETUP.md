# 🔑 دليل إعداد متغيرات البيئة
# Environment Variables Setup Guide

## 📋 نظرة عامة

تم تحديث المشروع ليقرأ جميع مفاتيح API من متغيرات البيئة بدلاً من القيم المُدمجة في الكود.

## 🛠️ الإعداد السريع

### 1. إنشاء ملف البيئة المحلي
```bash
# انسخ ملف القالب إلى ملف البيئة المحلي
cp .env.example .env.local
```

### 2. تحديث القيم في `.env.local`
قم بتحديث القيم التالية حسب احتياجاتك:

```bash
# Google Analytics (اختياري)
NEXT_PUBLIC_GA_ID=G-YOUR_ACTUAL_GA_ID

# Search Console Verification (اختياري)
GOOGLE_SITE_VERIFICATION=your-actual-verification-code
BING_VERIFICATION=your-actual-bing-verification-code
YANDEX_VERIFICATION=your-actual-yandex-verification-code

# Security Tokens (اختياري ولكن مُوصى به)
NEXTAUTH_SECRET=your-strong-random-secret
REVALIDATE_TOKEN=your-secure-random-token
```

## 🔐 مفاتيح Supabase

### القيم الحالية (جاهزة للاستخدام):
```bash
NEXT_PUBLIC_SUPABASE_URL="https://ckjjqlbzflnxolflixkq.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

✅ **هذان المفتاحان كافيان لتشغيل التطبيق بالكامل**
- جلب البيانات من قاعدة البيانات
- عرض المحتوى التعليمي
- جميع وظائف الموقع

## 📁 هيكل الملفات

```
├── .env.example          # قالب متغيرات البيئة
├── .env.local           # القيم الفعلية (محمي بـ .gitignore)
├── integrations/
│   └── supabase/
│       └── client.ts    # يقرأ من متغيرات البيئة
└── lib/
    └── config.ts        # إعدادات الموقع المركزية
```

## ⚠️ ملاحظات أمنية

1. **لا تضع مفاتيح حساسة في `.env.example`**
2. **استخدم `.env.local` للقيم الفعلية**
3. **تأكد من أن `.env.local` في `.gitignore`**
4. **استخدم `NEXT_PUBLIC_` فقط للقيم التي يمكن كشفها للعميل**

## 🚀 التشغيل

بعد إعداد `.env.local`:

```bash
# تطوير
npm run dev

# إنتاج
npm run build
npm start
```

## 🔍 استكشاف الأخطاء

### خطأ: "متغير البيئة مفقود"
```
Error: متغير البيئة NEXT_PUBLIC_SUPABASE_URL مفقود
```

**الحل:**
1. تأكد من وجود ملف `.env.local`
2. تأكد من أن المتغير موجود في الملف
3. أعد تشغيل الخادم

### خطأ: "Supabase connection failed"
1. تحقق من صحة `NEXT_PUBLIC_SUPABASE_URL`
2. تحقق من صحة `NEXT_PUBLIC_SUPABASE_ANON_KEY`
3. تأكد من أن مشروع Supabase نشط

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من هذا الدليل
2. راجع ملف `CONFIGURATION_GUIDE.md`
3. تأكد من أن جميع المتغيرات المطلوبة موجودة
