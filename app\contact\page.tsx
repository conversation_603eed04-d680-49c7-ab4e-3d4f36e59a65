import { Metadata } from 'next'
import dynamic from 'next/dynamic'
import { Mail, MapPin, Phone, Clock, MessageSquare, Send, User, HelpCircle } from 'lucide-react'

// Dynamic imports for client components
const Header = dynamic(() => import('@/components/Header'), { ssr: false })
const Breadcrumb = dynamic(() => import('@/components/Breadcrumb').then(mod => ({ default: mod.Breadcrumb })), { ssr: false })

export const metadata: Metadata = {
  title: 'اتصل بنا - منصة التعليم المغربي',
  description: 'تواصل مع فريق منصة التعليم المغربي - معلومات الاتصال والدعم الفني والاستفسارات التعليمية',
  keywords: [
    'اتصل بنا',
    'تواصل معنا',
    'دعم فني',
    'خدمة العملاء',
    'استفسارات',
    'مساعدة',
    'بريد إلكتروني',
    'هاتف',
    'عنوان',
    'منصة التعليم المغربي',
    'دعم تعليمي',
    'مساعدة طلاب'
  ],
  openGraph: {
    title: 'اتصل بنا - منصة التعليم المغربي',
    description: 'تواصل مع فريق منصة التعليم المغربي للحصول على المساعدة والدعم',
    type: 'website',
    url: 'https://www.talamid.ma/contact',
  },
  alternates: {
    canonical: '/contact',
  },
  robots: {
    index: true,
    follow: true,
  },
}

const breadcrumbItems = [
  { label: 'الرئيسية', href: '/' },
  { label: 'اتصل بنا', href: '/contact' }
]

export default function ContactPage() {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ContactPage",
            "name": "اتصل بنا",
            "description": "صفحة التواصل مع منصة التعليم المغربي",
            "url": "https://www.talamid.ma/contact",
            "mainEntity": {
              "@type": "Organization",
              "name": "منصة التعليم المغربي",
              "url": "https://www.talamid.ma",
              "contactPoint": [
                {
                  "@type": "ContactPoint",
                  "telephone": "+212-XXX-XXXXXX",
                  "contactType": "customer service",
                  "areaServed": "MA",
                  "availableLanguage": ["Arabic", "French"]
                },
                {
                  "@type": "ContactPoint",
                  "email": "<EMAIL>",
                  "contactType": "customer service",
                  "areaServed": "MA"
                }
              ],
              "address": {
                "@type": "PostalAddress",
                "addressCountry": "MA",
                "addressRegion": "المغرب"
              }
            }
          })
        }}
      />

      <div className="min-h-screen flex flex-col">
        <Header />

        <div className="container mx-auto px-4 py-10 arabic-text">
          <Breadcrumb items={breadcrumbItems} className="mb-6" />

          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <MessageSquare className="w-16 h-16 text-primary mx-auto mb-4" />
              <h1 className="text-4xl font-bold mb-4 text-primary">اتصل بنا</h1>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                نحن هنا لمساعدتك! تواصل معنا للحصول على الدعم أو الإجابة على استفساراتك
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-12">
              {/* معلومات الاتصال */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-2xl font-bold mb-6 text-primary">معلومات الاتصال</h2>
                  
                  <div className="space-y-6">
                    {/* البريد الإلكتروني */}
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <Mail className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">البريد الإلكتروني</h3>
                        <p className="text-muted-foreground mb-2">للاستفسارات العامة والدعم الفني</p>
                        <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                          <EMAIL>
                        </a>
                      </div>
                    </div>

                    {/* الهاتف */}
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <Phone className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">الهاتف</h3>
                        <p className="text-muted-foreground mb-2">للمساعدة الفورية والاستفسارات العاجلة</p>
                        <a href="tel:+212XXXXXXXXX" className="text-primary hover:underline">
                          +212-XXX-XXXXXX
                        </a>
                      </div>
                    </div>

                    {/* العنوان */}
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <MapPin className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">العنوان</h3>
                        <p className="text-muted-foreground mb-2">مقر منصة التعليم المغربي</p>
                        <p className="text-foreground">
                          المملكة المغربية<br />
                          المغرب
                        </p>
                      </div>
                    </div>

                    {/* ساعات العمل */}
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="bg-primary/10 p-3 rounded-lg">
                        <Clock className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold mb-1">ساعات العمل</h3>
                        <p className="text-muted-foreground mb-2">أوقات الرد على الاستفسارات</p>
                        <div className="space-y-1 text-foreground">
                          <p>الاثنين - الجمعة: 9:00 ص - 6:00 م</p>
                          <p>السبت: 9:00 ص - 2:00 م</p>
                          <p>الأحد: مغلق</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* أنواع الاستفسارات */}
                <div className="bg-card text-card-foreground p-6 rounded-xl shadow-lg">
                  <h3 className="text-xl font-bold mb-4">أنواع الاستفسارات التي نساعد فيها</h3>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <HelpCircle className="w-5 h-5 text-primary" />
                      <span>مشاكل تقنية في الموقع</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <HelpCircle className="w-5 h-5 text-primary" />
                      <span>استفسارات حول المحتوى التعليمي</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <HelpCircle className="w-5 h-5 text-primary" />
                      <span>اقتراحات لتحسين المنصة</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <HelpCircle className="w-5 h-5 text-primary" />
                      <span>الإبلاغ عن أخطاء في المحتوى</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <HelpCircle className="w-5 h-5 text-primary" />
                      <span>طلبات الشراكة والتعاون</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <HelpCircle className="w-5 h-5 text-primary" />
                      <span>استفسارات حول الخصوصية والبيانات</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* نموذج الاتصال */}
              <div className="bg-card text-card-foreground p-8 rounded-xl shadow-lg">
                <h2 className="text-2xl font-bold mb-6 text-primary">أرسل لنا رسالة</h2>
                
                <form className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium mb-2">
                        الاسم الأول *
                      </label>
                      <div className="relative">
                        <User className="absolute right-3 top-3 w-5 h-5 text-muted-foreground" />
                        <input
                          type="text"
                          id="firstName"
                          name="firstName"
                          required
                          className="w-full pr-10 pl-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                          placeholder="أدخل اسمك الأول"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium mb-2">
                        الاسم الأخير *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        required
                        className="w-full px-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="أدخل اسمك الأخير"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2">
                      البريد الإلكتروني *
                    </label>
                    <div className="relative">
                      <Mail className="absolute right-3 top-3 w-5 h-5 text-muted-foreground" />
                      <input
                        type="email"
                        id="email"
                        name="email"
                        required
                        className="w-full pr-10 pl-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium mb-2">
                      رقم الهاتف (اختياري)
                    </label>
                    <div className="relative">
                      <Phone className="absolute right-3 top-3 w-5 h-5 text-muted-foreground" />
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        className="w-full pr-10 pl-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="+212-XXX-XXXXXX"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium mb-2">
                      موضوع الرسالة *
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      required
                      className="w-full px-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="">اختر موضوع الرسالة</option>
                      <option value="technical">مشكلة تقنية</option>
                      <option value="content">استفسار حول المحتوى</option>
                      <option value="suggestion">اقتراح تحسين</option>
                      <option value="error">الإبلاغ عن خطأ</option>
                      <option value="partnership">طلب شراكة</option>
                      <option value="privacy">استفسار حول الخصوصية</option>
                      <option value="other">أخرى</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium mb-2">
                      الرسالة *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      className="w-full px-3 py-2 border border-input rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                      placeholder="اكتب رسالتك هنا... يرجى تقديم أكبر قدر من التفاصيل لمساعدتنا في فهم استفسارك بشكل أفضل."
                    ></textarea>
                  </div>

                  <div className="flex items-center space-x-2 space-x-reverse">
                    <input
                      type="checkbox"
                      id="privacy"
                      name="privacy"
                      required
                      className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                    <label htmlFor="privacy" className="text-sm">
                      أوافق على{' '}
                      <a href="/privacy" className="text-primary hover:underline">
                        سياسة الخصوصية
                      </a>{' '}
                      و{' '}
                      <a href="/terms" className="text-primary hover:underline">
                        شروط الاستخدام
                      </a>
                    </label>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-primary text-primary-foreground py-3 px-6 rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center space-x-2 space-x-reverse"
                  >
                    <Send className="w-5 h-5" />
                    <span>إرسال الرسالة</span>
                  </button>
                </form>
              </div>
            </div>

            {/* معلومات إضافية */}
            <div className="mt-12 grid md:grid-cols-3 gap-6">
              <div className="bg-blue-50 border border-blue-200 p-6 rounded-xl text-center">
                <Clock className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold mb-2 text-blue-800">وقت الاستجابة</h3>
                <p className="text-sm text-blue-700">
                  نرد على الرسائل خلال 24-48 ساعة في أيام العمل
                </p>
              </div>
              
              <div className="bg-green-50 border border-green-200 p-6 rounded-xl text-center">
                <MessageSquare className="w-8 h-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold mb-2 text-green-800">دعم مجاني</h3>
                <p className="text-sm text-green-700">
                  جميع خدمات الدعم والمساعدة مجانية تماماً
                </p>
              </div>
              
              <div className="bg-purple-50 border border-purple-200 p-6 rounded-xl text-center">
                <HelpCircle className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold mb-2 text-purple-800">مساعدة شاملة</h3>
                <p className="text-sm text-purple-700">
                  نساعدك في جميع جوانب استخدام المنصة
                </p>
              </div>
            </div>

            {/* الأسئلة الشائعة */}
            <div className="mt-12 bg-card text-card-foreground p-8 rounded-xl shadow-lg">
              <h2 className="text-2xl font-bold mb-6 text-primary">الأسئلة الشائعة</h2>
              <div className="space-y-4">
                <details className="border border-border rounded-lg p-4">
                  <summary className="font-semibold cursor-pointer">كيف يمكنني تحميل المواد التعليمية؟</summary>
                  <p className="mt-2 text-muted-foreground arabic-paragraph">
                    يمكنك تحميل المواد التعليمية مجاناً من خلال النقر على زر &quot;تحميل&quot; الموجود في كل درس أو تمرين.
                  </p>
                </details>
                
                <details className="border border-border rounded-lg p-4">
                  <summary className="font-semibold cursor-pointer">هل المحتوى متوافق مع المناهج المغربية؟</summary>
                  <p className="mt-2 text-muted-foreground arabic-paragraph">
                    نعم، جميع المحتويات معدة وفقاً للمناهج المغربية الرسمية المعتمدة من وزارة التربية الوطنية.
                  </p>
                </details>
                
                <details className="border border-border rounded-lg p-4">
                  <summary className="font-semibold cursor-pointer">كيف يمكنني الإبلاغ عن خطأ في المحتوى؟</summary>
                  <p className="mt-2 text-muted-foreground arabic-paragraph">
                    يمكنك الإبلاغ عن أي خطأ من خلال نموذج الاتصال أعلاه أو إرسال بريد إلكتروني مباشر إلينا.
                  </p>
                </details>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
