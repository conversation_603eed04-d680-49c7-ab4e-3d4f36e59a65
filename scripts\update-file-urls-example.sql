-- مثال على كيفية تحديث الروابط في قاعدة البيانات لتكون نسبية
-- Example: How to update file URLs in database to be relative

-- ===== قبل التحديث (Before Update) =====
-- الروابط الحالية كاملة:
-- exercise_image_url: 'https://d.tolabi.net/files/math/grade7/exercise1.pdf'
-- solution_image_url: 'https://d.tolabi.net/files/math/grade7/solution1.pdf'

-- ===== بعد التحديث (After Update) =====
-- الروابط الجديدة نسبية:
-- exercise_image_url: 'files/math/grade7/exercise1.pdf'
-- solution_image_url: 'files/math/grade7/solution1.pdf'

-- 🔧 سكريبت تحديث جدول التمارين (exercises)
UPDATE exercises 
SET exercise_image_url = REPLACE(exercise_image_url, 'https://d.tolabi.net/', '')
WHERE exercise_image_url LIKE 'https://d.tolabi.net/%';

UPDATE exercises 
SET solution_image_url = REPLACE(solution_image_url, 'https://d.tolabi.net/', '')
WHERE solution_image_url LIKE 'https://d.tolabi.net/%';

-- 🔧 سكريبت تحديث جدول الفروض (homeworks)
UPDATE homeworks 
SET exercise_image_url = REPLACE(exercise_image_url, 'https://d.tolabi.net/', '')
WHERE exercise_image_url LIKE 'https://d.tolabi.net/%';

UPDATE homeworks 
SET solution_image_url = REPLACE(solution_image_url, 'https://d.tolabi.net/', '')
WHERE solution_image_url LIKE 'https://d.tolabi.net/%';

-- 🔧 سكريبت تحديث جدول الملخصات (summaries)
UPDATE summaries 
SET exercise_image_url = REPLACE(exercise_image_url, 'https://d.tolabi.net/', '')
WHERE exercise_image_url LIKE 'https://d.tolabi.net/%';

-- 🔧 سكريبت تحديث جدول الامتحانات (exams)
UPDATE exams 
SET exercise_image_url = REPLACE(exercise_image_url, 'https://d.tolabi.net/', '')
WHERE exercise_image_url LIKE 'https://d.tolabi.net/%';

UPDATE exams 
SET solution_image_url = REPLACE(solution_image_url, 'https://d.tolabi.net/', '')
WHERE solution_image_url LIKE 'https://d.tolabi.net/%';

-- ===== التحقق من النتائج (Verify Results) =====

-- عرض عينة من التمارين بعد التحديث
SELECT id, exercise_image_url, solution_image_url 
FROM exercises 
WHERE exercise_image_url IS NOT NULL 
LIMIT 5;

-- عرض عينة من الفروض بعد التحديث
SELECT id, exercise_image_url, solution_image_url 
FROM homeworks 
WHERE exercise_image_url IS NOT NULL 
LIMIT 5;

-- عرض عينة من الملخصات بعد التحديث
SELECT id, exercise_image_url 
FROM summaries 
WHERE exercise_image_url IS NOT NULL 
LIMIT 5;

-- عرض عينة من الامتحانات بعد التحديث
SELECT id, exercise_image_url, solution_image_url 
FROM exams 
WHERE exercise_image_url IS NOT NULL 
LIMIT 5;

-- ===== إضافة بيانات جديدة (Adding New Data) =====

-- مثال على إضافة تمرين جديد بروابط نسبية
INSERT INTO exercises (
    id, 
    lesson_id, 
    hint, 
    exercise_image_url, 
    solution_image_url,
    student_input_type
) VALUES (
    'new_exercise_1',
    'g7_math_tamrin1',
    'تمرين جديد في الرياضيات',
    'files/math/grade7/new_exercise.pdf',  -- رابط نسبي
    'files/math/grade7/new_solution.pdf',  -- رابط نسبي
    'text'
);

-- مثال على إضافة فرض جديد بروابط نسبية
INSERT INTO homeworks (
    id, 
    lesson_id, 
    hint, 
    exercise_image_url, 
    solution_image_url,
    student_input_type
) VALUES (
    'new_homework_1',
    'g7_math_homework1',
    'فرض جديد في الرياضيات',
    'files/math/grade7/new_homework.pdf',  -- رابط نسبي
    'files/math/grade7/new_homework_solution.pdf',  -- رابط نسبي
    'text'
);

-- مثال على إضافة ملخص جديد برابط نسبي
INSERT INTO summaries (
    id, 
    lesson_id, 
    hint, 
    exercise_image_url
) VALUES (
    'new_summary_1',
    'g7_math_summary1',
    'ملخص جديد في الرياضيات',
    'files/math/grade7/new_summary.pdf'  -- رابط نسبي
);

-- مثال على إضافة امتحان جديد بروابط نسبية
INSERT INTO exams (
    id, 
    lesson_id, 
    hint, 
    exercise_image_url, 
    solution_image_url
) VALUES (
    'new_exam_1',
    'g7_math_exam1',
    'امتحان جديد في الرياضيات',
    'files/math/grade7/new_exam.pdf',  -- رابط نسبي
    'files/math/grade7/new_exam_solution.pdf'  -- رابط نسبي
);

-- ===== ملاحظات مهمة (Important Notes) =====

/*
🎯 فوائد استخدام الروابط النسبية:

1. المرونة في تغيير الخادم:
   - يمكن تغيير NEXT_PUBLIC_FILE_SERVER_URL دون تعديل قاعدة البيانات
   - سهولة النقل بين البيئات (تطوير، اختبار، إنتاج)

2. سهولة الصيانة:
   - تحديث واحد في متغيرات البيئة يؤثر على جميع الملفات
   - لا حاجة لتحديث آلاف الروابط في قاعدة البيانات

3. دعم خوادم متعددة:
   - يمكن إعداد خوادم احتياطية بسهولة
   - تحويل تلقائي للروابط حسب البيئة

📝 كيفية العمل:
- قاعدة البيانات تحتوي على: 'files/math/grade7/exercise1.pdf'
- النظام يحولها إلى: 'https://d.tolabi.net/files/math/grade7/exercise1.pdf'
- إذا غيرت الخادم إلى 'https://cdn.tolabi.net'، ستصبح:
  'https://cdn.tolabi.net/files/math/grade7/exercise1.pdf'

🔧 متغيرات البيئة المطلوبة:
NEXT_PUBLIC_FILE_SERVER_URL=https://d.tolabi.net
NEXT_PUBLIC_FILE_FALLBACK_DOMAINS=https://d.tolabi.net,https://files.tolabi.net
NEXT_PUBLIC_FILE_PATH_PREFIX=
*/
